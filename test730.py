import os
import re
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

# 设置支持中文的字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'Microsoft YaHei', 'WenQuanYi Micro Hei']
plt.rcParams['axes.unicode_minus'] = False

# 全局变量存储同缆组配置
CABLE_GROUPS = {}

def load_cable_groups_from_json(config_file="cable_groups.json"):
    """从JSON配置文件加载同缆组"""
    global CABLE_GROUPS
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
            # 将字符串键转换为整数键
            CABLE_GROUPS = {int(k): v for k, v in config['cable_groups'].items()}
        print(f"成功从 {config_file} 加载了 {len(CABLE_GROUPS)} 个同缆组")
        return True
    except FileNotFoundError:
        print(f"配置文件 {config_file} 未找到")
        return False
    except Exception as e:
        print(f"加载配置文件失败: {e}")
        return False

def load_cable_groups_from_csv(config_file="cable_groups.csv"):
    """从CSV配置文件加载同缆组"""
    global CABLE_GROUPS
    try:
        df = pd.read_csv(config_file)
        CABLE_GROUPS = {}
        for _, row in df.iterrows():
            group_id = int(row['group_id'])
            filename = row['filename']
            if group_id not in CABLE_GROUPS:
                CABLE_GROUPS[group_id] = []
            CABLE_GROUPS[group_id].append(filename)
        print(f"成功从 {config_file} 加载了 {len(CABLE_GROUPS)} 个同缆组")
        return True
    except FileNotFoundError:
        print(f"配置文件 {config_file} 未找到")
        return False
    except Exception as e:
        print(f"加载配置文件失败: {e}")
        return False

def save_cable_groups_to_json(config_file="cable_groups.json"):
    """将当前同缆组保存到JSON配置文件"""
    try:
        config = {
            "cable_groups": {str(k): v for k, v in CABLE_GROUPS.items()}
        }
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        print(f"同缆组配置已保存到 {config_file}")
        return True
    except Exception as e:
        print(f"保存配置文件失败: {e}")
        return False

def save_cable_groups_to_csv(config_file="cable_groups.csv"):
    """将当前同缆组保存到CSV配置文件"""
    try:
        data = []
        for group_id, filenames in CABLE_GROUPS.items():
            for filename in filenames:
                data.append({'group_id': group_id, 'filename': filename})
        df = pd.DataFrame(data)
        df.to_csv(config_file, index=False)
        print(f"同缆组配置已保存到 {config_file}")
        return True
    except Exception as e:
        print(f"保存配置文件失败: {e}")
        return False

def get_cable_group(filename):
    """获取文件所属的同缆组"""
    for group_id, files in CABLE_GROUPS.items():
        if filename in files:
            return group_id
    return -1  # 返回-1表示不属于任何已知组

def process_otdr_file(file_path, normalize=True, debug=False):
    """处理单个OTDR文件,去除首尾异常点并进行Min-Max归一化

    Args:
        file_path: OTDR文件路径
        normalize: 是否进行Min-Max归一化，默认为True
        debug: 是否输出调试信息，默认为False

    Returns:
        numpy.ndarray: 处理后的数据，包含位置和数值两列
    """
    with open(file_path, 'r') as f:
        content = f.read()

    # 提取数据点（跳过非数字行）
    data_lines = []
    in_data_section = False
    for line in content.split('\n'):
        if "POS(km)" in line:
            in_data_section = True
            continue
        if in_data_section and line.strip():
            if re.match(r"^\d+\.\d+\s+-?\d+\.\d+", line):
                data_lines.append(line)

    # 转换为数值
    data = []
    for line in data_lines:
        pos, val = map(float, line.strip().split())
        data.append([pos, val])

    original_data = np.array(data)

    if debug:
        print(f"\n文件: {os.path.basename(file_path)}")
        print(f"原始数据点数: {len(original_data)}")
        if len(original_data) > 0:
            print(f"原始数据范围: 位置[{original_data[0,0]:.3f}, {original_data[-1,0]:.3f}], 数值[{original_data[:,1].min():.3f}, {original_data[:,1].max():.3f}]")

    # 去除首端前10个点
    if len(original_data) > 10:
        data = original_data[10:]
        if debug:
            print(f"去除首端10个点后: {len(data)} 个点，位置范围[{data[0,0]:.3f}, {data[-1,0]:.3f}]")
    else:
        data = original_data

    # 去除尾端50个点
    if len(data) > 50:
        data = data[:-50]
        if debug:
            print(f"去除尾端50个点后: {len(data)} 个点，位置范围[{data[0,0]:.3f}, {data[-1,0]:.3f}]")

    # Min-Max数据归一化
    if normalize and len(data) > 0:
        values = data[:, 1]  # 获取数值列
        min_val = np.min(values)
        max_val = np.max(values)

        if debug:
            print(f"归一化前数值范围: [{min_val:.3f}, {max_val:.3f}]")

        # 避免除零错误
        if max_val != min_val:
            # 应用Min-Max归一化公式: x_n = (x - min(x)) / (max(x) - min(x))
            data[:, 1] = (values - min_val) / (max_val - min_val)
            if debug:
                print(f"归一化后数值范围: [{data[:,1].min():.3f}, {data[:,1].max():.3f}]")
        else:
            # 如果所有值相同，归一化为0
            data[:, 1] = 0
            if debug:
                print("所有数值相同，归一化为0")

    return data

def process_folder(folder_path, normalize=True, debug=False):
    """处理整个文件夹及其子文件夹的OTDR文件

    Args:
        folder_path: 根文件夹路径
        normalize: 是否进行Min-Max归一化，默认为True
        debug: 是否输出调试信息，默认为False

    Returns:
        dict: 以文件名为键，数据为值的字典
    """
    file_dict = {}
    processed_count = 0

    def process_single_file(file_path):
        """处理单个文件并返回数据"""
        try:
            data = process_otdr_file(file_path, normalize=normalize, debug=debug)
            if len(data) > 0:
                return data
        except Exception as e:
            print(f"处理文件失败: {os.path.basename(file_path)} - {str(e)}")
        return None

    def walk_folder(current_path):
        """递归遍历文件夹"""
        nonlocal processed_count
        for item in os.listdir(current_path):
            item_path = os.path.join(current_path, item)

            relative_path = os.path.relpath(os.path.dirname(item_path), folder_path)
            current_prefix = relative_path.replace("\\", "_").replace("/", "_")
            if current_prefix == ".":
                current_prefix = ""

            if os.path.isfile(item_path) and item.endswith(".asc"):
                data = process_single_file(item_path)
                if data is not None:
                    new_filename = f"{current_prefix}_{item}" if current_prefix else item
                    file_dict[new_filename] = data
                    processed_count += 1

            elif os.path.isdir(item_path):
                walk_folder(item_path)

    print(f"开始处理文件夹: {folder_path}")
    walk_folder(folder_path)
    print(f"成功处理 {processed_count} 个OTDR文件")

    return file_dict

def plot_all_curves(file_dict, title_suffix=""):
    """绘制所有曲线在同一图中"""
    plt.figure(figsize=(12, 6))

    # 直接绘制所有曲线
    for filename, data in file_dict.items():
        plt.plot(data[:, 0], data[:, 1],
                linewidth=1.5,
                alpha=0.7,
                label=filename)

    plt.title(f"OTDR曲线综合比对{title_suffix}", pad=20)
    plt.xlabel("Position (km)")
    plt.ylabel("Value (dB)" if not title_suffix else "Normalized Value")
    plt.grid(True, alpha=0.3)
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.tight_layout()
    plt.show()

def plot_comparison(original_data, normalized_data):
    """对比显示原始数据和归一化后的数据"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

    # 绘制原始数据
    for filename, data in original_data.items():
        ax1.plot(data[:, 0], data[:, 1],
                linewidth=1.5,
                alpha=0.7,
                label=filename)

    ax1.set_title("原始OTDR曲线（去除首尾异常点）", pad=20)
    ax1.set_xlabel("Position (km)")
    ax1.set_ylabel("Value (dB)")
    ax1.grid(True, alpha=0.3)
    ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')

    # 绘制归一化数据
    for filename, data in normalized_data.items():
        ax2.plot(data[:, 0], data[:, 1],
                linewidth=1.5,
                alpha=0.7,
                label=filename)

    ax2.set_title("Min-Max归一化后的OTDR曲线", pad=20)
    ax2.set_xlabel("Position (km)")
    ax2.set_ylabel("Normalized Value [0,1]")
    ax2.grid(True, alpha=0.3)
    ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left')

    plt.tight_layout()
    plt.show()

def print_normalization_stats(original_data, normalized_data):
    """打印归一化统计信息"""
    print("\n=== 归一化统计信息 ===")
    for filename in original_data.keys():
        orig_values = original_data[filename][:, 1]
        norm_values = normalized_data[filename][:, 1]

        print(f"\n文件: {filename}")
        print(f"  原始数据范围: [{orig_values.min():.3f}, {orig_values.max():.3f}]")
        print(f"  归一化后范围: [{norm_values.min():.3f}, {norm_values.max():.3f}]")
        print(f"  原始数据标准差: {orig_values.std():.3f}")
        print(f"  归一化后标准差: {norm_values.std():.3f}")

def plot_three_way_comparison(folder_path, sample_files=3):
    """三重对比：原始数据 vs 去除异常点 vs 归一化"""
    print(f"\n=== 创建三重对比图（前{sample_files}个文件） ===")

    # 获取原始数据（完全不处理）
    raw_data = {}
    processed_count = 0

    def walk_folder_raw(current_path):
        nonlocal processed_count
        for item in os.listdir(current_path):
            if processed_count >= sample_files:
                break
            item_path = os.path.join(current_path, item)

            if os.path.isfile(item_path) and item.endswith(".asc"):
                try:
                    # 读取完全原始的数据
                    with open(item_path, 'r') as f:
                        content = f.read()

                    data_lines = []
                    in_data_section = False
                    for line in content.split('\n'):
                        if "POS(km)" in line:
                            in_data_section = True
                            continue
                        if in_data_section and line.strip():
                            if re.match(r"^\d+\.\d+\s+-?\d+\.\d+", line):
                                data_lines.append(line)

                    data = []
                    for line in data_lines:
                        pos, val = map(float, line.strip().split())
                        data.append([pos, val])

                    if len(data) > 0:
                        raw_data[item] = np.array(data)
                        processed_count += 1
                except Exception as e:
                    print(f"读取原始数据失败: {item} - {str(e)}")

            elif os.path.isdir(item_path):
                walk_folder_raw(item_path)

    walk_folder_raw(folder_path)

    # 获取去除异常点的数据
    trimmed_data = {}
    for filename in raw_data.keys():
        file_path = None
        for root, dirs, files in os.walk(folder_path):
            if filename in files:
                file_path = os.path.join(root, filename)
                break

        if file_path:
            trimmed_data[filename] = process_otdr_file(file_path, normalize=False)

    # 获取归一化数据
    normalized_data = {}
    for filename in raw_data.keys():
        file_path = None
        for root, dirs, files in os.walk(folder_path):
            if filename in files:
                file_path = os.path.join(root, filename)
                break

        if file_path:
            normalized_data[filename] = process_otdr_file(file_path, normalize=True)

    # 创建三重对比图
    fig, axes = plt.subplots(1, 3, figsize=(20, 6))

    # 原始数据
    for filename, data in raw_data.items():
        axes[0].plot(data[:, 0], data[:, 1], linewidth=1.5, alpha=0.7, label=filename)
    axes[0].set_title("原始OTDR数据（未处理）", pad=20)
    axes[0].set_xlabel("Position (km)")
    axes[0].set_ylabel("Value (dB)")
    axes[0].grid(True, alpha=0.3)
    axes[0].legend()

    # 去除异常点后的数据
    for filename, data in trimmed_data.items():
        axes[1].plot(data[:, 0], data[:, 1], linewidth=1.5, alpha=0.7, label=filename)
    axes[1].set_title("去除首尾异常点后", pad=20)
    axes[1].set_xlabel("Position (km)")
    axes[1].set_ylabel("Value (dB)")
    axes[1].grid(True, alpha=0.3)
    axes[1].legend()

    # 归一化数据
    for filename, data in normalized_data.items():
        axes[2].plot(data[:, 0], data[:, 1], linewidth=1.5, alpha=0.7, label=filename)
    axes[2].set_title("Min-Max归一化后", pad=20)
    axes[2].set_xlabel("Position (km)")
    axes[2].set_ylabel("Normalized Value [0,1]")
    axes[2].grid(True, alpha=0.3)
    axes[2].legend()

    plt.tight_layout()
    plt.show()

    # 打印统计信息
    print("\n=== 数据处理统计 ===")
    for filename in raw_data.keys():
        raw = raw_data[filename]
        trimmed = trimmed_data[filename]
        normalized = normalized_data[filename]

        print(f"\n文件: {filename}")
        print(f"  原始数据: {len(raw)} 个点, 位置[{raw[0,0]:.3f}, {raw[-1,0]:.3f}], 数值[{raw[:,1].min():.3f}, {raw[:,1].max():.3f}]")
        print(f"  去除异常点后: {len(trimmed)} 个点, 位置[{trimmed[0,0]:.3f}, {trimmed[-1,0]:.3f}], 数值[{trimmed[:,1].min():.3f}, {trimmed[:,1].max():.3f}]")
        print(f"  归一化后: {len(normalized)} 个点, 位置[{normalized[0,0]:.3f}, {normalized[-1,0]:.3f}], 数值[{normalized[:,1].min():.3f}, {normalized[:,1].max():.3f}]")


# 主函数
if __name__ == "__main__":
    print("\n=== 开始文件处理程序 ===")

    # 尝试加载同缆组配置文件
    print("\n=== 加载同缆组配置 ===")
    config_loaded = False

    # 优先尝试加载JSON配置文件
    if os.path.exists("cable_groups.json"):
        config_loaded = load_cable_groups_from_json("cable_groups.json")
    # 如果JSON不存在，尝试加载CSV配置文件
    elif os.path.exists("cable_groups.csv"):
        config_loaded = load_cable_groups_from_csv("cable_groups.csv")

    if not config_loaded:
        print("警告: 未找到配置文件，将使用空的同缆组配置")
        print("请创建 cable_groups.json 或 cable_groups.csv 配置文件")

    # 显示加载的同缆组信息
    if CABLE_GROUPS:
        print(f"已加载同缆组信息:")
        for group_id, files in CABLE_GROUPS.items():
            print(f"  组 {group_id}: {len(files)} 个文件")

    folder_path = "C:/Users/<USER>/Desktop/干线测试"

    print("\n=== 处理OTDR数据 ===")
    # 处理原始数据（不归一化，开启调试模式查看前几个文件）
    print("处理原始数据（去除首尾异常点）...")
    original_data = process_folder(folder_path, normalize=False, debug=True)

    # 处理归一化数据
    print("\n处理归一化数据（去除首尾异常点 + Min-Max归一化）...")
    normalized_data = process_folder(folder_path, normalize=True, debug=False)

    if not original_data:
        print("未找到有效的OTDR文件！")
    else:
        print(f"成功读取 {len(original_data)} 个OTDR文件")

        # 显示归一化统计信息
        print_normalization_stats(original_data, normalized_data)

        # 对比显示原始数据和归一化数据
        print("\n=== 显示对比图表 ===")
        plot_comparison(original_data, normalized_data)