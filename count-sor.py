import os

def count_sor_files(root_folder):
    """
    遍历指定文件夹及其所有子文件夹，统计 .sor 文件的总数
    :param root_folder: 要扫描的根目录路径
    :return: .sor 文件的总数
    """
    sor_count = 0
    # 使用 os.walk 遍历所有子目录
    for dirpath, dirnames, filenames in os.walk(root_folder):
        for filename in filenames:
            if filename.lower().endswith('.sor'):  # 忽略大小写
                sor_count += 1
    return sor_count

# —— 主程序 ——
if __name__ == "__main__":
    # 请将下面的路径替换为你实际的大文件夹路径
    folder_path = r"C:\Users\<USER>\Desktop\佛山联通\2024年第四季度干线测试"  # 例如：r"C:\Users\<USER>\Documents\BigFolder"

    if not os.path.exists(folder_path):
        print(f"错误：路径不存在：{folder_path}")
    elif not os.path.isdir(folder_path):
        print(f"错误：路径不是文件夹：{folder_path}")
    else:
        total = count_sor_files(folder_path)
        print(f"总共找到 {total} 个 .sor 文件。")