# 同缆组配置文件使用说明

## 概述

现在您可以使用独立的配置文件来管理同缆组，而不需要修改代码。支持JSON和CSV两种格式。

## 配置文件格式

### 1. JSON格式 (cable_groups.json)

```json
{
  "cable_groups": {
    "1": [
      "ShundeYongfeng_DianxinNanqu_48F_01.asc",
      "ShundeYongfeng_DianxinNanqu_48F_02.asc"
    ],
    "2": [
      "ShundeYongfeng_DianxinNanqu_48F_11.asc",
      "ShundeYongfeng_DianxinNanqu_48F_12.asc"
    ]
  }
}
```

### 2. CSV格式 (cable_groups.csv)

```csv
group_id,filename
1,ShundeYongfeng_DianxinNanqu_48F_01.asc
1,ShundeYongfeng_DianxinNanqu_48F_02.asc
2,ShundeYongfeng_DianxinNanqu_48F_11.asc
2,ShundeYongfeng_DianxinNanqu_48F_12.asc
```

## 使用方法

### 方法1: 直接编辑配置文件

1. **编辑JSON文件**: 使用任何文本编辑器打开 `cable_groups.json`
2. **编辑CSV文件**: 使用Excel或文本编辑器打开 `cable_groups.csv`

### 方法2: 使用配置管理工具

运行配置管理工具：
```bash
python cable_config_manager.py
```

该工具提供以下功能：
- 查看所有组
- 添加文件到组
- 从组中移除文件
- 移动文件到其他组
- 创建新组
- 删除组
- 保存为JSON/CSV格式
- 格式转换

### 方法3: 在代码中动态管理

```python
from cable_config_manager import CableConfigManager

# 创建管理器
manager = CableConfigManager()

# 加载现有配置
manager.load_from_json("cable_groups.json")

# 添加新文件到组
manager.add_file_to_group(1, "new_file.asc")

# 创建新组
manager.create_new_group(["file1.asc", "file2.asc"])

# 保存配置
manager.save_to_json("cable_groups.json")
```

## 程序自动加载

修改后的 `test730.py` 会自动：

1. 优先加载 `cable_groups.json`
2. 如果JSON不存在，加载 `cable_groups.csv`
3. 如果都不存在，使用空配置并显示警告

## 配置文件优势

### 相比代码中硬编码的优势：

1. **灵活性**: 无需修改代码即可更新同缆组
2. **可维护性**: 配置与代码分离，便于管理
3. **可读性**: JSON和CSV格式都易于阅读和编辑
4. **版本控制**: 可以单独对配置文件进行版本管理
5. **多人协作**: 不同人员可以独立维护配置文件

### JSON vs CSV 格式对比：

| 特性 | JSON | CSV |
|------|------|-----|
| 可读性 | 结构清晰 | 表格形式 |
| 编辑工具 | 文本编辑器 | Excel/文本编辑器 |
| 文件大小 | 稍大 | 较小 |
| 嵌套支持 | 支持 | 不支持 |
| 推荐场景 | 程序配置 | 数据交换 |

## 最佳实践

1. **备份配置**: 修改前备份原配置文件
2. **验证格式**: 使用配置管理工具验证文件格式
3. **版本控制**: 将配置文件纳入版本控制系统
4. **文档记录**: 记录每次配置变更的原因

## 故障排除

### 常见问题：

1. **配置文件不存在**
   - 程序会显示警告并使用空配置
   - 使用配置管理工具创建新配置

2. **JSON格式错误**
   - 检查括号、引号是否匹配
   - 使用JSON验证工具检查格式

3. **CSV格式错误**
   - 确保有 `group_id,filename` 表头
   - 检查逗号分隔符是否正确

4. **文件编码问题**
   - 确保文件使用UTF-8编码
   - 避免使用记事本编辑（可能产生BOM）

## 示例操作

### 添加新的同缆组：

**JSON方式**:
```json
"19": [
  "NewCable_01.asc",
  "NewCable_02.asc"
]
```

**CSV方式**:
```csv
19,NewCable_01.asc
19,NewCable_02.asc
```

### 修改现有组：

只需在配置文件中添加或删除相应的文件名即可。

## 总结

通过使用独立的配置文件，您现在可以：
- 轻松管理同缆组而无需修改代码
- 使用多种工具和方法来编辑配置
- 提高配置管理的效率和可维护性

建议优先使用JSON格式，因为它结构更清晰，更适合程序配置。如果需要在Excel中编辑，可以使用CSV格式或通过配置管理工具进行格式转换。
